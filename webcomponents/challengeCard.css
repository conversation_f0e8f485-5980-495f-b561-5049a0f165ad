.challenge-card {
  display: flex;
  width: 327px;
  align-items: flex-start;
  border-radius: 8px;
  background: var(--cards-challenge-card-background-default, #FFF);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 2px 24px 0px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.challenge-card-image {
  width: 88px;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 8px 0px 0px 8px;
  background: lightgray 50% / cover no-repeat;
}

.challenge-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.challenge-card-content {
  display: flex;
  padding: 16px 15px 16px 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  flex: 1 0 0;
  border-radius: 0px 8px 8px 0px;
  position: relative;
}

.challenge-card-title {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  color: var(--text-headline-green, #005E3F);
  text-overflow: ellipsis;
  margin: 0 0 4px 0;
  
  /* Body 1/semibold */
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 21.6px; /* 135% */
  letter-spacing: 0.15px;
}

.challenge-card-info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

.challenge-card-info-icon {
  width: 16px;
  height: 16px;
}

.challenge-card-info-text {
  flex: 1 0 0;
  color: var(--text-body-default, #293033);
  margin: 0;

  /* Body 2/regular */
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18.9px; /* 135% */
  letter-spacing: 0.15px;
}

/* Status Pills */
.challenge-card .active-pill {
  display: inline-flex;
  padding: 4px 8px;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  background-color: var(--accent-blue);
  margin-top: 4px;
}

.challenge-card .done-pill {
  display: inline-flex;
  padding: 4px 8px;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  background-color: var(--secondary-green);
  margin-top: 4px;
}

.challenge-card .active-pill p,
.challenge-card .done-pill p {
  margin: 0;
  color: var(--aok-grn-8);
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 16.2px; /* 135% */
  letter-spacing: 0.15px;
}
