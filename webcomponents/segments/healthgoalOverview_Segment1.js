import { html } from "lit-html";
import "./healthgoalOverview_Segments.css";
import { healthgoalCardActive } from "../healthgoalCardActive.js";
import { healthgoalCardInactive } from "../healthgoalCardInactive.js";
import { infoCardSlider } from "../infoCardSlider.js";
import { sectionSubtitle } from "../sectionSubtitle.js";
import imgKomootHG from "../../img/healthgoals/komoot_hg.jpg";
import imgPeople from "../../img/people_hugging.jpg";
import imgKomootPortrait from "../../img/healthgoals/komoot_portrait.png";
import imgNatur from "../../img/healthgoals/hg_aktivInDerNatur.jpg";
import imgErnaehrung from "../../img/healthgoals/hg_issDichGesund.jpg";
import imgBewegung from "../../img/healthgoals/hg_mehrBewegungImAlltag.jpg";
import imgGelassenheit from "../../img/healthgoals/hg_mehrGelassenheit.jpg";
import imgMuskeln from "../../img/healthgoals/hg_staerkeDeineMuskeln.jpg";
import imgAusdauer from "../../img/healthgoals/hg_steigereDeineAusdauer.jpg";
import iconTagSport from "../../svg/icons/icon_tag_sport.svg";
import iconTagSleep from "../../svg/icons/icon_tag_sleep.svg";
import iconTagPsych from "../../svg/icons/icon_tag_brain.svg";
import iconTagFood from "../../svg/icons/icon_tag_food.svg";
import iconMoneypig from "../../svg/icons/icon_moneypig.svg";
import iconBell from "../../svg/icons/icon_bell.svg";
import iconTimelater from "../../svg/icons/icon_timelater.svg";
import { createChips } from "../chip_filterChip.js";
import { createCardFilter, addCategoryAttributes } from "../../src/utils/cardFilter.js";
import { calculateChallengeProgress, HEALTH_GOAL_CONFIG, appStorage } from "../../utils.js";

// Erstelle Filter-Funktion
const filterCards = createCardFilter(
  '.healthgoal-cards-container',
  '.healthgoal-card'
);

/* Filter Chips */
const chips = createChips("Bewegung Schlaf Psyche Ernährung", filterCards);

// Kategorien für die Karten (alle 8 Gesundheitsziele)
const categories = [
  "Bewegung", // Fit in deiner Umgebung
  "Schlaf",   // Gesunder Schlaf
  "Bewegung", // Aktiv in der Natur
  "Ernährung", // Iss Dich gesund
  "Bewegung", // Mehr Bewegung im Alltag
  "Psyche",   // Finde zu mehr Gelassenheit
  "Bewegung", // Stärke Deine Muskeln
  "Bewegung"  // Steigere Deine Ausdauer
];

/**
 * Erstellt den Inhalt für Segment 1 der Gesundheitsziele-Übersicht
 * Berechnet dynamisch die abgeschlossenen Challenges basierend auf der 80%-Regel
 * @returns {TemplateResult} Das Template für Segment 1
 */
export const healthgoalOverview_segment1Content = () => {
  // Berechne die aktuellen Challenge-Fortschritte für das aktive Gesundheitsziel
  const challengeProgress = calculateChallengeProgress('fitUmgebung');

  // Umfassendes Debugging für Challenge-Status
  console.log('=== HEALTH GOAL DEBUG INFO ===');
  console.log('Active Health Goal:', appStorage._data.activeHealthGoals);
  console.log('Health Goal Level:', appStorage._data.healthGoalLevel);
  console.log('Challenge Progress Summary:', {
    completedChallenges: challengeProgress.completedChallenges,
    totalChallenges: challengeProgress.totalChallenges,
    healthGoalCompletionPercentage: challengeProgress.completionPercentage
  });

  console.log('Individual Challenge Status:');
  challengeProgress.challengeDetails.forEach(challenge => {
    console.log(`  ${challenge.name}:`, {
      started: challenge.started,
      trainings: `${challenge.completedTrainings}/${challenge.totalTrainings}`,
      completion: `${challenge.completionPercentage}%`,
      isCompleted: challenge.isCompleted,
      completedFlag: challenge.completedFlag
    });
  });

  console.log('Raw AppStorage Challenges:', appStorage._data.challenges);
  console.log('=== END DEBUG INFO ===');

  return html`
    <div class="tabpadding">
      <div class="screen-centered content-padding">
        <h2 class="tabheadline">Dein aktives Ziel</h2>
        <div class="healthgoal-cards">
          <a data-navigate="/healthgoals-overview/hg-fitUmgebung">
            ${healthgoalCardActive({
              cardImage: imgKomootHG,
              tagCategory: "Bewegung",
              tagIcon: iconTagSport,
              tagColor: "--tag-sport",
              tagTextColor: "--tag-text",
              healthgoalTitle: "Fit in deiner Umgebung",
              healthgoalCoop: "In Kooperation komoot",
              pillText: "Gerade aktiv",
              pillColor: "--accent-blue",
              goalinfoIcon1: iconMoneypig,
              goalinfoText1: "1500 Bonuspunkte",
              goalinfoIcon2: iconTimelater,
              goalinfoText2: "max. 90 Tage",
              completedChallenges: challengeProgress.completedChallenges,
              totalChallenges: challengeProgress.totalChallenges,
            })}
          </a>
        </div>
      </div>
      <div class="content-padding firstcard-no-top-padding">
        <!-- Info Box Push Notification -->
        <div class="standard-container">
          ${infoCardSlider({
            icoPath: iconBell,
            title: "Du möchtest nichts vergessen?",
            text: "Stelle hier meine Erinnerungen an Dich ein",
            linkText: "Einstellungen",
          })}
        </div>
        ${sectionSubtitle("Alle Gesundheitsziele")}
        <!-- Sub Title here -->
        <div class="standard-container">
          ${chips.template}
          <!-- Filter Chips here -->
        </div>
        <div class="healthgoal-cards-container">
          ${!appStorage._data.activeHealthGoals.fitUmgebung ? healthgoalCardInactive({
            cardImage: imgKomootPortrait,
            tagCategory: "Bewegung",
            tagIcon: iconTagSport,
            tagColor: "--tag-sport",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Fit in deiner Umgebung",
            healthgoalCoop: "In Kooperation mit Komoot",
            pillText: "",
            pillColor: "--primary-brand",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
            link: "/healthgoals-overview/hg-fitUmgebung"
          }) : ''}
          ${healthgoalCardInactive({
            cardImage: imgPeople,
            tagCategory: "Schlaf",
            tagIcon: iconTagSleep,
            tagColor: "--tag-sleep",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Gesunder Schlaf",
            healthgoalCoop: "",
            pillText: "",
            pillColor: "--primary-brand",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
          })}
          ${healthgoalCardInactive({
            cardImage: imgNatur,
            tagCategory: "Bewegung",
            tagIcon: iconTagSport,
            tagColor: "--tag-sport",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Aktiv in der Natur",
            healthgoalCoop: "",
            pillText: "",
            pillColor: "--primary-brand",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
            dataCategory: "Bewegung"
          })}
          ${healthgoalCardInactive({
            cardImage: imgErnaehrung,
            tagCategory: "Ernährung",
            tagIcon: iconTagFood,
            tagColor: "--tag-food",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Iss Dich gesund",
            healthgoalCoop: "",
            pillText: "",
            pillColor: "--primary-brand",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
          })}
          ${healthgoalCardInactive({
            cardImage: imgBewegung,
            tagCategory: "Bewegung",
            tagIcon: iconTagSport,
            tagColor: "--tag-sport",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Mehr Bewegung im Alltag",
            healthgoalCoop: "",
            pillText: "",
            pillColor: "--primary-brand",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
          })}
          ${healthgoalCardInactive({
            cardImage: imgGelassenheit,
            tagCategory: "Psyche",
            tagIcon: iconTagPsych,
            tagColor: "--tag-psych",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Finde zu mehr Gelassenheit",
            healthgoalCoop: "",
            pillText: "",
            pillColor: "--primary-brand",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
          })}
          ${healthgoalCardInactive({
            cardImage: imgMuskeln,
            tagCategory: "Bewegung",
            tagIcon: iconTagSport,
            tagColor: "--tag-sport",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Stärke Deine Muskeln",
            healthgoalCoop: "",
            pillText: "",
            pillColor: "--primary-brand",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
          })}
          ${healthgoalCardInactive({
            cardImage: imgAusdauer,
            tagCategory: "Bewegung",
            tagIcon: iconTagSport,
            tagColor: "--tag-sport",
            tagTextColor: "--tag-text",
            healthgoalTitle: "Steigere Deine Ausdauer",
            healthgoalCoop: "",
            pillText: "",
            pillColor: "--primary-brand",
            goalinfoIcon1: iconMoneypig,
            goalinfoText1: "1500 Bonuspunkte",
            goalinfoIcon2: iconTimelater,
            goalinfoText2: "max. 90 Tage",
          })}
        </div>
      </div>
    </div>
  `;
};

// Initialisiere die Chips nach dem Rendern
export const initializeHealthgoalSegment1 = () => {
  setTimeout(() => {
    chips.initialize();

    // Füge Kategorie-Attribute zu den Karten hinzu
    addCategoryAttributes(
      '.healthgoal-cards-container',
      '.healthgoal-card',
      categories
    );
  }, 0);
};
